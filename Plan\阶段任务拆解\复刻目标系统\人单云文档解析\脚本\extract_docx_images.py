import zipfile
import os
import sys
from pathlib import Path

def extract_images_from_docx(docx_path, output_dir):
    """
    从Word文档中提取所有图片
    
    参数:
        docx_path: Word文档的路径
        output_dir: 图片输出目录
    
    返回:
        提取的图片数量
    """
    try:
        # 确保输出目录存在
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)
        
        # Word文档实际上是一个ZIP文件，可以直接解压
        count = 0
        with zipfile.ZipFile(docx_path) as docx_zip:
            # 图片通常存储在word/media目录下
            for file_info in docx_zip.infolist():
                if file_info.filename.startswith('word/media/'):
                    # 提取图片文件名
                    image_name = os.path.basename(file_info.filename)
                    # 设置输出路径
                    output_path = os.path.join(output_dir, image_name)
                    # 提取图片
                    with open(output_path, 'wb') as f:
                        f.write(docx_zip.read(file_info.filename))
                    count += 1
                    print(f"已提取图片: {image_name}")
        
        return count
    
    except Exception as e:
        print(f"提取图片时出错: {str(e)}")
        return 0

def main():
    # 设置文档路径
    docx_path = "examples/unofficial_20250304_dev_scripts/dos/PlaywrightMCP/人单云-基础版操作说明书V1.6.docx"
    
    # 设置图片输出目录
    output_dir = "extracted_images"
    
    # 检查文件是否存在
    if not os.path.exists(docx_path):
        print(f"文件不存在: {docx_path}")
        sys.exit(1)
    
    # 提取图片
    count = extract_images_from_docx(docx_path, output_dir)
    
    if count > 0:
        print(f"\n成功提取了 {count} 张图片到 '{output_dir}' 目录")
        print(f"图片保存在: {os.path.abspath(output_dir)}")
    else:
        print("未能从文档中提取任何图片")

if __name__ == "__main__":
    main()
