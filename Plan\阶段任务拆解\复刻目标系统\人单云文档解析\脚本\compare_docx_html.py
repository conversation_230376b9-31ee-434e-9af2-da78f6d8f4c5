import os
import re
import sys
from docx import Document
from bs4 import BeautifulSoup
from collections import defaultdict

def extract_docx_structure(docx_path):
    """
    提取Word文档的结构信息，包括章节和图片
    
    参数:
        docx_path: Word文档路径
        
    返回:
        包含文档结构信息的字典
    """
    try:
        # 打开Word文档
        doc = Document(docx_path)
        
        # 提取文本内容
        paragraphs = []
        for para in doc.paragraphs:
            if para.text.strip():
                paragraphs.append(para.text.strip())
        
        # 提取图片数量
        image_count = 0
        for rel in doc.part.rels.values():
            if "image" in rel.target_ref:
                image_count += 1
        
        # 提取章节结构
        headings = []
        for para in doc.paragraphs:
            if para.style.name.startswith('Heading'):
                level = int(para.style.name.replace('Heading', ''))
                headings.append((level, para.text.strip()))
        
        return {
            'paragraphs': paragraphs,
            'image_count': image_count,
            'headings': headings,
            'total_paragraphs': len(paragraphs)
        }
    
    except Exception as e:
        print(f"处理Word文档时出错: {str(e)}")
        return None

def extract_html_structure(html_path):
    """
    提取HTML文件的结构信息，包括章节和图片
    
    参数:
        html_path: HTML文件路径
        
    返回:
        包含文档结构信息的字典
    """
    try:
        # 打开HTML文件
        with open(html_path, 'r', encoding='utf-8') as f:
            html_content = f.read()
        
        # 解析HTML
        soup = BeautifulSoup(html_content, 'html.parser')
        
        # 提取文本内容
        paragraphs = []
        for p in soup.find_all('p'):
            if p.text.strip() and not p.find('img') and 'caption' not in p.get('class', []):
                paragraphs.append(p.text.strip())
        
        # 提取图片数量
        image_count = len(soup.find_all('img'))
        
        # 提取章节结构
        headings = []
        for i in range(1, 7):  # h1 到 h6
            for h in soup.find_all(f'h{i}'):
                headings.append((i, h.text.strip()))
        
        return {
            'paragraphs': paragraphs,
            'image_count': image_count,
            'headings': headings,
            'total_paragraphs': len(paragraphs)
        }
    
    except Exception as e:
        print(f"处理HTML文件时出错: {str(e)}")
        return None

def compare_structures(docx_structure, html_structure):
    """
    比较Word文档和HTML文件的结构
    
    参数:
        docx_structure: Word文档结构
        html_structure: HTML文件结构
        
    返回:
        比较结果
    """
    results = {}
    
    # 比较段落数量
    docx_para_count = docx_structure['total_paragraphs']
    html_para_count = html_structure['total_paragraphs']
    para_diff = abs(docx_para_count - html_para_count)
    para_match_rate = min(docx_para_count, html_para_count) / max(docx_para_count, html_para_count) * 100
    
    results['paragraph_comparison'] = {
        'docx_count': docx_para_count,
        'html_count': html_para_count,
        'difference': para_diff,
        'match_rate': para_match_rate
    }
    
    # 比较图片数量
    docx_img_count = docx_structure['image_count']
    html_img_count = html_structure['image_count']
    img_diff = abs(docx_img_count - html_img_count)
    img_match_rate = min(docx_img_count, html_img_count) / max(docx_img_count, html_img_count) * 100
    
    results['image_comparison'] = {
        'docx_count': docx_img_count,
        'html_count': html_img_count,
        'difference': img_diff,
        'match_rate': img_match_rate
    }
    
    # 比较章节结构
    docx_headings = docx_structure['headings']
    html_headings = html_structure['headings']
    
    heading_matches = 0
    for docx_level, docx_text in docx_headings:
        for html_level, html_text in html_headings:
            if docx_text == html_text:
                heading_matches += 1
                break
    
    heading_match_rate = heading_matches / max(len(docx_headings), len(html_headings)) * 100
    
    results['heading_comparison'] = {
        'docx_count': len(docx_headings),
        'html_count': len(html_headings),
        'matches': heading_matches,
        'match_rate': heading_match_rate
    }
    
    # 计算总体匹配率
    overall_match_rate = (para_match_rate + img_match_rate + heading_match_rate) / 3
    
    results['overall_match_rate'] = overall_match_rate
    
    return results

def main():
    # 设置文件路径
    docx_path = "../人单云-基础版操作说明书V1.6.docx"
    html_path = "../网页/document.html"
    
    # 检查文件是否存在
    if not os.path.exists(docx_path):
        print(f"Word文档不存在: {docx_path}")
        sys.exit(1)
    
    if not os.path.exists(html_path):
        print(f"HTML文件不存在: {html_path}")
        sys.exit(1)
    
    print("开始比较Word文档和HTML文件的内容...")
    
    # 提取Word文档结构
    print("\n提取Word文档结构...")
    docx_structure = extract_docx_structure(docx_path)
    if not docx_structure:
        print("无法提取Word文档结构，比较终止。")
        sys.exit(1)
    
    # 提取HTML文件结构
    print("\n提取HTML文件结构...")
    html_structure = extract_html_structure(html_path)
    if not html_structure:
        print("无法提取HTML文件结构，比较终止。")
        sys.exit(1)
    
    # 比较结构
    print("\n比较文档结构...")
    results = compare_structures(docx_structure, html_structure)
    
    # 输出比较结果
    print("\n=== 比较结果 ===")
    
    print("\n段落比较:")
    print(f"Word文档段落数: {results['paragraph_comparison']['docx_count']}")
    print(f"HTML文件段落数: {results['paragraph_comparison']['html_count']}")
    print(f"差异: {results['paragraph_comparison']['difference']}")
    print(f"匹配率: {results['paragraph_comparison']['match_rate']:.2f}%")
    
    print("\n图片比较:")
    print(f"Word文档图片数: {results['image_comparison']['docx_count']}")
    print(f"HTML文件图片数: {results['image_comparison']['html_count']}")
    print(f"差异: {results['image_comparison']['difference']}")
    print(f"匹配率: {results['image_comparison']['match_rate']:.2f}%")
    
    print("\n章节比较:")
    print(f"Word文档章节数: {results['heading_comparison']['docx_count']}")
    print(f"HTML文件章节数: {results['heading_comparison']['html_count']}")
    print(f"匹配章节数: {results['heading_comparison']['matches']}")
    print(f"匹配率: {results['heading_comparison']['match_rate']:.2f}%")
    
    print("\n总体匹配率: {:.2f}%".format(results['overall_match_rate']))
    
    # 判断是否一致
    if results['overall_match_rate'] >= 90:
        print("\n结论: Word文档和HTML文件内容基本一致。")
    elif results['overall_match_rate'] >= 70:
        print("\n结论: Word文档和HTML文件内容大部分一致，但存在一些差异。")
    else:
        print("\n结论: Word文档和HTML文件内容存在较大差异。")

if __name__ == "__main__":
    main()
