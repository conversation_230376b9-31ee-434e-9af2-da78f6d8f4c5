import os
import sys
from bs4 import BeautifulSoup

def analyze_html_content(html_path):
    """
    分析HTML文件的内容结构
    
    参数:
        html_path: HTML文件路径
    """
    try:
        # 打开HTML文件
        with open(html_path, 'r', encoding='utf-8') as f:
            html_content = f.read()
        
        # 解析HTML
        soup = BeautifulSoup(html_content, 'html.parser')
        
        # 提取文本内容
        paragraphs = []
        for p in soup.find_all('p'):
            if p.text.strip() and not p.find('img') and 'caption' not in p.get('class', []):
                paragraphs.append(p.text.strip())
        
        # 提取图片
        images = soup.find_all('img')
        image_count = len(images)
        
        # 提取标题和章节
        headings = []
        for i in range(1, 7):  # h1 到 h6
            for h in soup.find_all(f'h{i}'):
                headings.append((i, h.text.strip()))
        
        # 检查目录结构
        toc_items = []
        for p in soup.find_all('p'):
            text = p.text.strip()
            if text.startswith('第') and ('章' in text or '节' in text):
                toc_items.append(text)
        
        # 输出分析结果
        print("\n=== HTML文件内容分析 ===")
        print(f"\n文件路径: {html_path}")
        print(f"总段落数: {len(paragraphs)}")
        print(f"总图片数: {image_count}")
        
        print("\n前10个段落:")
        for i, para in enumerate(paragraphs[:10]):
            print(f"{i+1}. {para[:50]}..." if len(para) > 50 else f"{i+1}. {para}")
        
        print("\n文档章节结构:")
        if headings:
            for level, text in headings[:20]:  # 显示前20个标题
                print(f"{'  ' * (level-1)}{'#' * level} {text}")
        else:
            # 如果没有使用h1-h6标签，尝试从段落中提取章节结构
            chapter_count = 0
            for para in paragraphs:
                if para.startswith('第') and '章' in para:
                    chapter_count += 1
                    print(f"# {para}")
                elif para.startswith(str(chapter_count) + '.') and len(para) < 100:
                    print(f"  ## {para}")
        
        print("\n目录项:")
        for item in toc_items[:20]:  # 显示前20个目录项
            print(f"- {item}")
        
        print(f"\n图片引用检查:")
        image_paths = set()
        for img in images[:10]:  # 显示前10个图片
            src = img.get('src', '')
            alt = img.get('alt', '')
            print(f"- 图片: {src}, 描述: {alt}")
            image_paths.add(src)
        
        # 检查图片引用路径
        print("\n图片引用路径分析:")
        path_patterns = {}
        for path in image_paths:
            base_dir = os.path.dirname(path)
            if base_dir in path_patterns:
                path_patterns[base_dir] += 1
            else:
                path_patterns[base_dir] = 1
        
        for pattern, count in path_patterns.items():
            print(f"- 路径模式: {pattern}, 使用次数: {count}")
        
        # 检查是否包含"人单云-基础版操作说明书"的关键内容
        key_phrases = [
            "人单云-基础版系统",
            "阿米巴落地困局",
            "系统价值",
            "功能模块介绍",
            "账号申请与权限配置",
            "角色组",
            "系统初始化",
            "组织结构初始化",
            "科目初始化",
            "巴科目初始化",
            "系统使用",
            "录入核算数据",
            "计划数据录入",
            "计划审批",
            "核算审批"
        ]
        
        print("\n关键内容检查:")
        html_text = ' '.join(paragraphs).lower()
        for phrase in key_phrases:
            if phrase.lower() in html_text:
                print(f"- ✓ 包含关键内容: {phrase}")
            else:
                print(f"- ✗ 缺少关键内容: {phrase}")
        
        # 检查文档完整性
        expected_chapters = [
            "系统简介",
            "账号申请与权限配置",
            "系统初始化",
            "系统使用"
        ]
        
        print("\n章节完整性检查:")
        for chapter in expected_chapters:
            found = False
            for para in paragraphs:
                if chapter in para:
                    found = True
                    break
            if found:
                print(f"- ✓ 包含章节: {chapter}")
            else:
                print(f"- ✗ 缺少章节: {chapter}")
        
        # 检查图片与文本的对应关系
        print("\n图片与文本对应关系检查:")
        caption_count = len(soup.find_all('p', class_='caption'))
        print(f"- 图片数量: {image_count}")
        print(f"- 图片说明数量: {caption_count}")
        if image_count == caption_count:
            print("- ✓ 每张图片都有对应的说明")
        else:
            print(f"- ✗ 图片和说明数量不匹配，差异: {abs(image_count - caption_count)}")
        
        return True
    
    except Exception as e:
        print(f"分析HTML文件时出错: {str(e)}")
        return False

def main():
    # 设置HTML文件路径
    html_path = "../网页/document.html"
    
    # 检查文件是否存在
    if not os.path.exists(html_path):
        print(f"HTML文件不存在: {html_path}")
        sys.exit(1)
    
    print("开始分析HTML文件内容...")
    
    # 分析HTML文件
    if analyze_html_content(html_path):
        print("\n分析完成！")
        print("\n总结:")
        print("1. 请检查上述分析结果，确认HTML文件是否包含了Word文档的所有内容")
        print("2. 特别注意图片引用路径是否正确")
        print("3. 确认章节结构是否完整")
        print("4. 确认关键内容是否都包含在HTML文件中")
    else:
        print("\n分析失败！")

if __name__ == "__main__":
    main()
