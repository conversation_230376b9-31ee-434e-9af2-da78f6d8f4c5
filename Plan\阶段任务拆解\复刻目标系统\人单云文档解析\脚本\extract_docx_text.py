import zipfile
import os
import sys
import re
import xml.etree.ElementTree as ET

def extract_text_from_docx(docx_path):
    """
    从Word文档中提取文本内容
    
    参数:
        docx_path: Word文档的路径
    
    返回:
        文档中的文本内容
    """
    try:
        # Word文档实际上是一个ZIP文件
        text_content = []
        
        with zipfile.ZipFile(docx_path) as docx_zip:
            # 主要文本内容存储在word/document.xml中
            xml_content = docx_zip.read('word/document.xml')
            
            # 解析XML
            root = ET.fromstring(xml_content)
            
            # 定义命名空间
            ns = {'w': 'http://schemas.openxmlformats.org/wordprocessingml/2006/main'}
            
            # 提取段落文本
            for paragraph in root.findall('.//w:p', ns):
                texts = []
                for text_element in paragraph.findall('.//w:t', ns):
                    if text_element.text:
                        texts.append(text_element.text)
                
                if texts:
                    text_content.append(''.join(texts))
        
        # 返回文本内容，每段用换行符分隔
        return '\n'.join(text_content)
    
    except Exception as e:
        return f"提取文档内容时出错: {str(e)}"

def main():
    # 设置文档路径
    docx_path = "examples/unofficial_20250304_dev_scripts/dos/PlaywrightMCP/人单云-基础版操作说明书V1.6.docx"
    
    # 检查文件是否存在
    if not os.path.exists(docx_path):
        print(f"文件不存在: {docx_path}")
        sys.exit(1)
    
    # 提取文本内容
    text_content = extract_text_from_docx(docx_path)
    
    # 输出文本内容到文件
    output_file = "extracted_text.txt"
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(text_content)
    
    print(f"文本内容已保存到文件: {os.path.abspath(output_file)}")
    
    # 打印前1000个字符作为预览
    preview_length = min(1000, len(text_content))
    print(f"\n文本内容预览 (前{preview_length}个字符):")
    print("-" * 80)
    print(text_content[:preview_length])
    print("-" * 80)

if __name__ == "__main__":
    main()
