import zipfile
import os
import sys
import re
import xml.etree.ElementTree as ET
from pathlib import Path
import shutil

def extract_docx_content_complete(docx_path, output_dir):
    """
    从Word文档中完整提取文本内容和图片，并保持它们的对应关系
    
    参数:
        docx_path: Word文档的路径
        output_dir: 输出目录
    """
    try:
        # 确保输出目录存在
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)
        
        # 创建图片目录
        images_dir = os.path.join(output_dir, "images")
        if not os.path.exists(images_dir):
            os.makedirs(images_dir)
        
        # Word文档实际上是一个ZIP文件
        with zipfile.ZipFile(docx_path) as docx_zip:
            # 提取所有图片
            image_files = {}
            for file_info in docx_zip.infolist():
                if file_info.filename.startswith('word/media/'):
                    image_name = os.path.basename(file_info.filename)
                    output_image_path = os.path.join(images_dir, image_name)
                    
                    with open(output_image_path, 'wb') as f:
                        f.write(docx_zip.read(file_info.filename))
                    
                    image_files[image_name] = output_image_path
                    print(f"已提取图片: {image_name}")
            
            # 主要文本内容存储在word/document.xml中
            xml_content = docx_zip.read('word/document.xml')
            
            # 解析XML
            root = ET.fromstring(xml_content)
            
            # 定义命名空间
            ns = {
                'w': 'http://schemas.openxmlformats.org/wordprocessingml/2006/main',
                'r': 'http://schemas.openxmlformats.org/officeDocument/2006/relationships',
                'a': 'http://schemas.openxmlformats.org/drawingml/2006/main',
                'wp': 'http://schemas.openxmlformats.org/drawingml/2006/wordprocessingDrawing',
                'pic': 'http://schemas.openxmlformats.org/drawingml/2006/picture'
            }
            
            # 读取文档关系
            rels_content = docx_zip.read('word/_rels/document.xml.rels')
            rels_root = ET.fromstring(rels_content)
            
            # 创建关系ID到图片文件名的映射
            rels_map = {}
            for rel in rels_root.findall('.//{http://schemas.openxmlformats.org/package/2006/relationships}Relationship'):
                rel_id = rel.get('Id')
                target = rel.get('Target')
                if target and target.startswith('media/'):
                    rels_map[rel_id] = target.split('/')[-1]
            
            # 提取内容
            html_content = []
            html_content.append('<!DOCTYPE html>')
            html_content.append('<html>')
            html_content.append('<head>')
            html_content.append('<meta charset="UTF-8">')
            html_content.append('<title>人单云-基础版操作说明书</title>')
            html_content.append('<style>')
            html_content.append('body { font-family: "Microsoft YaHei", SimSun, sans-serif; line-height: 1.6; margin: 20px; background-color: #f9f9f9; }')
            html_content.append('h1, h2, h3, h4 { color: #333; margin-top: 20px; border-bottom: 1px solid #eee; padding-bottom: 5px; }')
            html_content.append('h1 { font-size: 24px; }')
            html_content.append('h2 { font-size: 20px; }')
            html_content.append('h3 { font-size: 18px; }')
            html_content.append('h4 { font-size: 16px; }')
            html_content.append('img { max-width: 100%; border: 1px solid #ddd; margin: 10px 0; box-shadow: 0 2px 5px rgba(0,0,0,0.1); }')
            html_content.append('.image-container { margin: 20px 0; padding: 10px; background-color: #fff; border-radius: 5px; box-shadow: 0 1px 3px rgba(0,0,0,0.1); }')
            html_content.append('.caption { font-size: 14px; color: #666; text-align: center; margin-top: 5px; }')
            html_content.append('p { margin: 10px 0; }')
            html_content.append('.container { max-width: 1000px; margin: 0 auto; background-color: #fff; padding: 20px; border-radius: 5px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }')
            html_content.append('</style>')
            html_content.append('</head>')
            html_content.append('<body>')
            html_content.append('<div class="container">')
            
            # 当前章节和小节
            current_heading = ""
            
            # 处理段落和图片
            for paragraph in root.findall('.//w:p', ns):
                # 检查是否是标题
                is_heading = False
                heading_level = 0
                
                # 查找段落样式
                for pPr in paragraph.findall('.//w:pPr', ns):
                    for pStyle in pPr.findall('.//w:pStyle', ns):
                        style_val = pStyle.get('{http://schemas.openxmlformats.org/wordprocessingml/2006/main}val')
                        if style_val and style_val.startswith('Heading'):
                            is_heading = True
                            try:
                                heading_level = int(style_val.replace('Heading', ''))
                            except:
                                heading_level = 1
                
                # 提取段落文本
                texts = []
                for text_element in paragraph.findall('.//w:t', ns):
                    if text_element.text:
                        texts.append(text_element.text)
                
                paragraph_text = ''.join(texts)
                
                # 如果是标题，添加适当的HTML标题标签
                if is_heading and paragraph_text:
                    html_tag = f'h{min(heading_level, 6)}'
                    html_content.append(f'<{html_tag}>{paragraph_text}</{html_tag}>')
                    current_heading = paragraph_text
                elif paragraph_text:
                    html_content.append(f'<p>{paragraph_text}</p>')
                
                # 查找图片
                for drawing in paragraph.findall('.//w:drawing', ns):
                    for blip in drawing.findall('.//{http://schemas.openxmlformats.org/drawingml/2006/main}blip', ns):
                        embed = blip.get('{http://schemas.openxmlformats.org/officeDocument/2006/relationships}embed')
                        if embed and embed in rels_map:
                            image_name = rels_map[embed]
                            
                            # 添加图片到HTML，包含章节信息
                            caption = f"图片: {image_name} - 相关章节: {current_heading}"
                            html_content.append(f'<div class="image-container">')
                            html_content.append(f'<img src="images/{image_name}" alt="{caption}">')
                            html_content.append(f'<p class="caption">{caption}</p>')
                            html_content.append(f'</div>')
            
            html_content.append('</div>')
            html_content.append('</body>')
            html_content.append('</html>')
            
            # 保存HTML文件
            html_file_path = os.path.join(output_dir, "document.html")
            with open(html_file_path, 'w', encoding='utf-8') as f:
                f.write('\n'.join(html_content))
            
            print(f"文档已转换为HTML，保存在: {html_file_path}")
            print(f"图片保存在: {images_dir}")
            
            return html_file_path
    
    except Exception as e:
        print(f"处理文档时出错: {str(e)}")
        return None

def main():
    # 设置文档路径
    docx_path = "../人单云-基础版操作说明书V1.6.docx"
    
    # 设置输出目录
    output_dir = "."
    
    # 检查文件是否存在
    if not os.path.exists(docx_path):
        print(f"文件不存在: {docx_path}")
        sys.exit(1)
    
    # 提取文档内容和图片
    html_file = extract_docx_content_complete(docx_path, output_dir)
    
    if html_file:
        print("\n处理完成！")
        print(f"您可以在浏览器中打开 {html_file} 查看文档内容和对应的图片")

if __name__ == "__main__":
    main()
