import os
import re
import sys

def fix_html_paths(html_file, output_file):
    """
    修复HTML文件中的图片引用路径
    
    参数:
        html_file: 原始HTML文件路径
        output_file: 输出HTML文件路径
    """
    try:
        # 读取HTML文件内容
        with open(html_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 修改图片引用路径
        fixed_content = content.replace('src="images/', 'src="../图片/')
        
        # 保存修改后的HTML文件
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(fixed_content)
        
        print(f"已修复HTML文件中的图片引用路径，保存在: {output_file}")
        return True
    
    except Exception as e:
        print(f"修复HTML文件时出错: {str(e)}")
        return False

def main():
    # 设置文件路径
    html_file = "../document.html"
    output_file = "../网页/document.html"
    
    # 检查文件是否存在
    if not os.path.exists(html_file):
        print(f"文件不存在: {html_file}")
        sys.exit(1)
    
    # 确保输出目录存在
    output_dir = os.path.dirname(output_file)
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    # 修复HTML文件中的图片引用路径
    if fix_html_paths(html_file, output_file):
        print("\n处理完成！")
        print(f"您可以在浏览器中打开 {output_file} 查看文档内容和对应的图片")

if __name__ == "__main__":
    main()
